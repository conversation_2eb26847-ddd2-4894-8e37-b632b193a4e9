import React from "react";

import Link from "next/link";
import styled from "styled-components";

const RightContentContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 20px;
  height: fit-content;
`;

const NotificationSection = styled.div`
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`;

const NotificationHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
`;

const NotificationTitle = styled.h3`
  font-size: 16px;
  font-weight: bold;
  line-height: 1;
  color: #243544;
`;

const ViewAllButton = styled(Link)`
  font-size: 14px;
  cursor: pointer;
  color: #007aff;
  line-height: 1;
`;

const NotificationItem = styled.div`
  padding: 12px 0;
  border-bottom: 1px solid #e2e3e5;

  &:first-child {
    border-top: 1px solid #e2e3e5;
  }
`;

const NotificationDate = styled.div`
  font-size: 12px;
  color: #999;
  margin-bottom: 8px;
`;

const NotificationText = styled.div`
  font-size: 14px;
  color: #333;
  line-height: 1.4;
`;

const MobilePreviewSection = styled.div`
  background: #f5f5f5;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const MobilePreviewTitle = styled.h3`
  font-size: 14px;
  color: #666;
  margin: 0 0 20px 0;
  text-align: center;
`;

const MobileFrame = styled.div`
  width: 280px;
  height: 500px;
  background: white;
  border-radius: 20px;
  border: 8px solid #333;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  position: relative;
`;

const MobileHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
`;

const MobileTitle = styled.h4`
  font-size: 14px;
  font-weight: bold;
  margin: 0;
  color: #333;
`;

const MobileSignal = styled.div`
  font-size: 12px;
  color: #666;
`;

const MobileContent = styled.div`
  font-size: 12px;
  line-height: 1.4;
  color: #333;
`;

const ClinicName = styled.div`
  font-size: 16px;
  font-weight: bold;
  margin: 10px 0;
  color: #1890ff;
`;

const ClinicInfo = styled.div`
  font-size: 11px;
  color: #666;
  margin-bottom: 15px;
`;

const MenuTabs = styled.div`
  display: flex;
  border-bottom: 1px solid #e8e8e8;
  margin-bottom: 10px;
`;

const MenuTab = styled.div<{ active?: boolean }>`
  padding: 8px 12px;
  font-size: 11px;
  cursor: pointer;
  border-bottom: 2px solid
    ${(props) => (props.active ? "#1890ff" : "transparent")};
  color: ${(props) => (props.active ? "#1890ff" : "#666")};
`;

export const RightContent: React.FC = () => {
  return (
    <RightContentContainer>
      <NotificationSection>
        <NotificationHeader>
          <NotificationTitle>掲載中のお知らせ</NotificationTitle>
          <ViewAllButton href="/clinic-map/news">すべて見る</ViewAllButton>
        </NotificationHeader>

        <NotificationItem>
          <NotificationDate>2023/06/01</NotificationDate>
          <NotificationText>休診日のお知らせ</NotificationText>
        </NotificationItem>

        <NotificationItem>
          <NotificationDate>2023/05/30</NotificationDate>
          <NotificationText>
            文書料金改定のお知らせ（4月1日より改定）
          </NotificationText>
        </NotificationItem>

        <NotificationItem>
          <NotificationDate>2023/05/15</NotificationDate>
          <NotificationText>院内におけるマスク着用の緩和</NotificationText>
        </NotificationItem>

        <NotificationItem>
          <NotificationDate>2023/04/20</NotificationDate>
          <NotificationText>年末年始の診療時間のお知らせ</NotificationText>
        </NotificationItem>

        <NotificationItem>
          <NotificationDate>2023/04/10</NotificationDate>
          <NotificationText>
            WEBでの初診予約受付を開始いたしました
          </NotificationText>
        </NotificationItem>
      </NotificationSection>

      <MobilePreviewSection>
        <MobilePreviewTitle>スマートフォン画面</MobilePreviewTitle>
        <MobileFrame>
          <MobileHeader>
            <MobileTitle>GMOクリニックマップ</MobileTitle>
            <MobileSignal>📶 🔋</MobileSignal>
          </MobileHeader>

          <ClinicName>VERDE CLINICお茶の水（ヘルスクリニック）</ClinicName>

          <ClinicInfo>
            内科・循環器内科・心療内科
            <br />
            月・火・水・木・金・土
            <br />
            〒101-0062 東京都千代田区神田駿河台3-2-1
            <br />
            新御茶ノ水駅より徒歩1分 / 御茶ノ水駅より徒歩3分
            <br />
            電話番号：03-1234-5678 / FAX：03-1234-5679
          </ClinicInfo>

          <MenuTabs>
            <MenuTab active>診療メニュー</MenuTab>
            <MenuTab>スタッフ</MenuTab>
          </MenuTabs>

          <MobileContent>
            内科
            <br />
            ☑ オンライン診療
            <br />
            <br />
            【オンライン診療】初診診療・アレルギー治療
            <br />
            ※ 初回診療時は来院が必要です
            <br />※ アレルギー治療は継続治療のみ対応
          </MobileContent>
        </MobileFrame>
      </MobilePreviewSection>
    </RightContentContainer>
  );
};
