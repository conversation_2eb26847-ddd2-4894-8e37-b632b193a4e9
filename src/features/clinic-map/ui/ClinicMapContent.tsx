import React from "react";

import { Layout } from "antd";
import styled from "styled-components";

import { PatientProvider } from "@/components/common/Patient/AddPatient/Providers/PatientProvider";

import { LeftContent } from "./left-content";
import { RightContent } from "./right-content";

const ClinicMapContentContainer = styled(Layout.Content)`
  display: grid;
  grid-template-columns: 380px 1fr;
  gap: 20px;
  padding: 20px 20px 0px 0px;
`;

export const ClinicMapContent: React.FC = () => {
  return (
    <PatientProvider>
      <ClinicMapContentContainer>
        <LeftContent />
        <RightContent />
      </ClinicMapContentContainer>
    </PatientProvider>
  );
};
