import { Controller, useFieldArray } from "react-hook-form";
import styled from "styled-components";

import { useGetApiPatientInforGetHokenSyaMstLazyQuery } from "@/apis/gql/operations/__generated__/patient-infor";
import { SvgIconDelete } from "@/components/ui/Icon/IconDelete";
import { IconButton } from "@/components/ui/IconButton";
import { Button } from "@/components/ui/NewButton";
import { Table } from "@/components/ui/Table";
import { TextInput } from "@/components/ui/TextInput";
import { useBufferLoader } from "@/hooks/useBufferLoader";

import type { Control, FieldArrayWithId } from "react-hook-form";
import type { TableColumnsType } from "antd";
import type { PrintFormType } from "../../types/print";

const StyledTextInput = styled(TextInput)`
  width: 140px;
  height: 28px;
`;

const Wrapper = styled.div``;

const StyledButton = styled(Button)`
  width: 140px;
  height: 28px;
`;

const AddPatient = styled.div`
  padding: 20px;
  text-align: center;
`;

const SelectHokenWrapper = styled.div`
  padding: 26px 24px;
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #e0e6ec;
`;

const SelectHokenHeading = styled.p`
  font-size: 16px;
  font-weight: bold;
`;

export const HokenSearchTableForm = ({
  control,
}: {
  control: Control<PrintFormType>;
}) => {
  const { append, remove, update, fields } = useFieldArray({
    name: "hokenlist",
    control,
  });

  const [searchHokensya, { loading: originLoading }] =
    useGetApiPatientInforGetHokenSyaMstLazyQuery();

  const { loading } = useBufferLoader({ originLoading });

  const searchHoken = async (index: number, hokenNum: string | undefined) => {
    if (!hokenNum) {
      remove(index);
      return;
    }

    const { data: hokensyaData } = await searchHokensya({
      variables: {
        keyword: hokenNum,
      },
    });

    const hokensya =
      hokensyaData?.getApiPatientInforSearchHokensyaMst?.data?.listData ?? [];

    const target = hokensya[0];

    if (!target) {
      update(index, {
        hokenId: "",
        displayName: "",
      });
      return;
    }

    update(index, {
      hokenId: target.hokensyaNo ?? "",
      displayName: target.name ?? "",
    });
  };

  const columns = [
    {
      title: "保険者番号",
      width: 170,
      render: (_, __, index) => (
        <Controller
          name={`hokenlist.${index}.hokenId`}
          control={control}
          render={({ field }) => (
            <StyledTextInput
              {...field}
              onBlur={async (e) => await searchHoken(index, e.target.value)}
            />
          )}
        />
      ),
    },
    {
      title: "名称",
      width: 250,
      render: (_, { displayName }) => displayName,
    },
    {
      title: "",
      width: 60,
      render: (_, __, index) => {
        return (
          <IconButton
            varient="icon-only"
            icon={<SvgIconDelete />}
            onClick={() => remove(index)}
          />
        );
      },
    },
  ] satisfies TableColumnsType<
    FieldArrayWithId<PrintFormType, "hokenlist", "id">
  >;

  return (
    <Wrapper>
      <SelectHokenWrapper>
        <SelectHokenHeading>保険指定</SelectHokenHeading>
      </SelectHokenWrapper>
      <Table
        rowKey="id"
        columns={columns}
        dataSource={fields}
        loading={loading}
      />
      <AddPatient>
        <StyledButton
          varient="standard-sr"
          onClick={() => {
            append({
              hokenId: "",
              displayName: "",
            });
          }}
        >
          保険者追加
        </StyledButton>
      </AddPatient>
    </Wrapper>
  );
};
