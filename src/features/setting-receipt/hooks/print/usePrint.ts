import { useState } from "react";

import { ApolloError } from "@apollo/client";

import { useErrorHand<PERSON> } from "@/hooks/useErrorHandler";
import { logger } from "@/utils/sentry-logger";

import { generateReceListPrint } from "../../utils/rest";

import type { PrintFormType, SokatuMst } from "../../types/print";

export const usePrint = () => {
  const [isLoading, setIsLoading] = useState(false);
  const { handleError } = useErrorHandler();

  const handleGenerateDocument = async ({
    includeTester,
    includeOutDrug,
    sort,
    ptlist,
    hokenlist,
    departmentId,
    doctorId,
    hokenKbn,
    seikyuYm,
    diskCount,
    diskKind,
    sokatuMst,
  }: PrintFormType & {
    sokatuMst: NonNullable<SokatuMst[number]>;
  }) => {
    setIsLoading(true);

    try {
      const newWindow = window.open("", "_blank");

      if (!newWindow) {
        return;
      }

      if (!sokatuMst) {
        return;
      }

      const { reportId, reportEdaNo, prefNo, dataKbn, printType } = sokatuMst;

      const targetIdList = (ptlist ?? hokenlist ?? [])
        .map((item) => item.ptId)
        .filter((id) => id !== "");

      const response = await generateReceListPrint({
        seikyuYm: Number(seikyuYm?.format("YYYYMM") ?? "0"),
        reportId,
        reportEdaNo,
        prefNo,
        dataKbn,
        includeTester,
        isIncludeOutDrug: includeOutDrug,
        sinYm: 0,
        hokenId: 0,
        departmentId,
        doctorId,
        hokenKbn,
        sort,
        printType,
        printPtIds: targetIdList,
        printHokensyaNos: targetIdList,
        welfareType:
          prefNo === 13 && reportEdaNo === 1 && reportId === 105 ? 1 : 0,
        diskCnt: diskCount,
        diskKind,
        // ReceSbt: プロパティにない？
        // PtId: 型の不整合？
      });

      if (response.headers["content-type"] === "application/pdf") {
        const pdfUrl = URL.createObjectURL(response.data);
        newWindow.location.href = pdfUrl;
        return;
      }

      // TODO: スマクリのエラー表示を使うか確認
      if (response.headers["content-type"] === "text/html") {
        const textHtml = await response.data.text();
        newWindow.document.open();
        newWindow.document.write(textHtml);
        newWindow.document.close();
        return;
      }

      throw new Error("content-type is not applicable");
    } catch (error) {
      logger({ error, message: "failed to generate print document" });
      if (error instanceof ApolloError || error instanceof Error) {
        handleError({ error, commonMessage: "対象の出力に失敗しました。" });
      }
    } finally {
      setIsLoading(false);
    }
  };

  return {
    isLoading,
    handleGenerateDocument,
  };
};
