import React from "react";

import styled, { css } from "styled-components";

type ToggleData = {
  text: string;
  value: string | number;
  isDisabled?: boolean;
};

type Props = {
  defaultValue?: string | number;
  className?: string;
  toggleData: ToggleData[];
  onChange: (checkedToggle: string | number) => void;
  width?: number;
};

const StyledWrapper = styled.div<{ width?: number }>`
  display: inline-flex;
  align-items: center;
  height: 36px;
  background-color: "#fbfcfe";
  border-radius: 6px;
  border: 1px solid #e2e3e5;
  width: ${({ width }) => (width ? `${width}px` : "100%")};
`;

const StyledLabel = styled.label<{ width: number }>`
  width: ${(props) => props.width}%;
  margin: 4px 3px 4px 4px;
`;

const StyledInput = styled.input`
  display: none;
`;

const StyledText = styled.div<{ checked: boolean; disabled?: boolean }>`
  height: 28px;
  border-radius: 3px;
  display: flex;
  justify-content: center;
  align-items: center;
  transition:
    background 0.1s,
    border 0.1s;

  ${({ checked }) =>
    checked
      ? css`
          background-color: #4dd0e1;
          color: #fff;
        `
      : css`
          color: #6a757d;
          cursor: pointer;

          &:hover {
            background-color: #eaf0f5;
          }
        `}

  ${({ disabled }) =>
    disabled &&
    css`
      opacity: 0.3;
      cursor: not-allowed;
    `}
`;

export const Toggle: React.FC<Props> = (props: Props) => {
  const { className, defaultValue, toggleData, onChange, width } = props;
  const elements = toggleData.map((eachToggle: ToggleData) => {
    return (
      <StyledLabel key={eachToggle.text} width={100 / toggleData.length}>
        <StyledInput
          type="radio"
          checked={eachToggle.value === defaultValue}
          onChange={() => {
            onChange(eachToggle.value);
          }}
          disabled={eachToggle.isDisabled}
        />
        <StyledText
          checked={eachToggle.value === defaultValue}
          disabled={eachToggle.isDisabled}
        >
          {eachToggle.text}
        </StyledText>
      </StyledLabel>
    );
  });

  return (
    <StyledWrapper width={width} className={className}>
      {elements}
    </StyledWrapper>
  );
};
