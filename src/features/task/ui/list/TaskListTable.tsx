import dayjs from "dayjs";
import { useRouter } from "next/router";
import { styled } from "styled-components";

import { TaskSortType } from "@/apis/gql/generated/types";
import {
  InfiniteScrollTable,
  TableDataCell,
} from "@/components/common/InfiniteScrollTable";
import { ContentLoading } from "@/components/ui/ContentLoading";
import { SvgIconFire } from "@/components/ui/Icon/IconFire";
import { formatYYYYMMDDWithSlash } from "@/utils/datetime-format";

import { TaskPatientInfoTooltip } from "./TaskPatientInfoTooltip";

import type { TableColumn } from "@/components/common/InfiniteScrollTable";
import type { SortOrder, Task, TaskStatus } from "@/apis/gql/generated/types";

const StyledTable = styled(InfiniteScrollTable<Task>)`
  tbody {
    td {
      border: 1px solid #e2e3e5;
    }
  }
`;

const ExpiredAtFireStyle = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
`;

const TextStyle = styled.div`
  color: #d41e1e;
  padding: 2px;
`;

const SvgIconsIconFireStyle = styled(SvgIconFire)``;

const LoadingWrapper = styled.div``;

type ExpiredAtProps = {
  expiredAt?: string;
  status: TaskStatus;
};
const ExpiredAt = ({ expiredAt, status }: ExpiredAtProps) => {
  if (!expiredAt) return null;
  const expiredAtFormat = formatYYYYMMDDWithSlash(new Date(expiredAt));
  const isExpired =
    dayjs().isAfter(dayjs(new Date(expiredAt))) && status.name !== "完了";

  if (isExpired) {
    return (
      <ExpiredAtFireStyle>
        <TextStyle>{expiredAtFormat}</TextStyle>
        <SvgIconsIconFireStyle />
      </ExpiredAtFireStyle>
    );
  }
  return <ExpiredAtFireStyle>{expiredAtFormat}</ExpiredAtFireStyle>;
};

type Props = {
  tasks?: Task[];
  loading: boolean;
  sortOrder?: SortOrder;
  sortType?: TaskSortType;
  isLoadingMore: boolean;
  onSortTasks: (sortField: string, sortOrder: SortOrder | null) => void;
  onLoadMore: (cursorId: string, cursorDate?: string) => void;
};

export const TaskTable = ({
  tasks,
  loading,
  sortOrder,
  sortType,
  isLoadingMore,
  onSortTasks,
  onLoadMore,
}: Props) => {
  const columns: TableColumn[] = [
    {
      title: "登録日",
      dataIndex: TaskSortType.CreatedDate,
      width: "120px",
      sortable: true,
    },
    {
      title: "カテゴリー",
      dataIndex: "category",
      width: "120px",
    },
    {
      title: "件名",
      dataIndex: "title",
      width: "380px",
    },
    {
      title: "起票者",
      dataIndex: "createdStaffName",
      width: "120px",
    },
    {
      title: "担当者",
      dataIndex: "responsibleStaffName",
      width: "120px",
    },
    {
      title: "ステータス",
      dataIndex: "status",
      width: "130px",
    },
    {
      title: "期限日",
      dataIndex: TaskSortType.ExpiredAt,
      width: "120px",
      sortable: true,
    },
    {
      title: "関連患者",
      dataIndex: "patient",
      width: "140px",
    },
  ];

  const handleLoadMore = (currentLastItem?: Task) => {
    if (!currentLastItem) return;
    const idCursor = currentLastItem.taskId.toString();

    let dateCursor: string | undefined;

    if (sortType === TaskSortType.CreatedDate) {
      dateCursor = currentLastItem.createdDate ?? "null";
    } else if (sortType === TaskSortType.ExpiredAt) {
      dateCursor = currentLastItem.expiredAt ?? "null";
    }

    onLoadMore(idCursor, dateCursor);
  };

  const { push } = useRouter();

  const handleSelectTask = (task: Task) => {
    push(`/task/${task.taskId}`);
  };

  if (loading) {
    return (
      <LoadingWrapper style={{ height: window.innerHeight - 480 }}>
        <ContentLoading />
      </LoadingWrapper>
    );
  }

  if (!tasks) return null;

  return (
    <StyledTable
      isLoadingMore={isLoadingMore}
      style={{ height: "100%" }}
      data={tasks}
      columns={columns}
      onLoadMore={() => handleLoadMore(tasks[tasks.length - 1])}
      onSort={onSortTasks}
      sortField={sortType}
      sortOrder={sortOrder}
      onRowClick={handleSelectTask}
      itemContent={(
        _,
        {
          createdDate,
          category,
          title,
          createdStaff,
          responsibleStaff,
          status,
          expiredAt,
          patient,
        },
      ) => (
        <>
          <TableDataCell align="center">
            {createdDate ? formatYYYYMMDDWithSlash(new Date(createdDate)) : ""}
          </TableDataCell>
          <TableDataCell align="center">{category.name}</TableDataCell>
          <TableDataCell>{title}</TableDataCell>
          <TableDataCell align="center">
            {createdStaff?.staffName}
          </TableDataCell>
          <TableDataCell align="center">
            {responsibleStaff?.staffName}
          </TableDataCell>
          <TableDataCell align="center">{status.name}</TableDataCell>
          <TableDataCell align="center">
            <ExpiredAt expiredAt={expiredAt} status={status} />
          </TableDataCell>
          <TableDataCell align="center">
            <TaskPatientInfoTooltip patient={patient} />
          </TableDataCell>
        </>
      )}
    />
  );
};
