import React from "react";

import dayjs from "dayjs";
import styled from "styled-components";

import { useScheduleView } from "../../hooks/useScheduleView";
import { useGetScheduleList } from "../../hooks/useGetScheduleList";

import { ScheduleHeader, BigCalendarView } from "./components";

import type { ScheduleEvent } from "./types";

const ScheduleSectionContainer = styled.div`
  height: 380px;
  border-radius: 12px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  padding: 20px;
`;

export const ScheduleSection = () => {
  const {
    currentView,
    currentDate,
    setCurrentView,
    navigateDate,
    handleDateChange,
  } = useScheduleView();

  const { schedules } = useGetScheduleList(
    {
      startDate: currentDate.toISOString(),
      endDate: currentDate.add(1, "month").toISOString(),
    },
    false,
    (schedules) => {
      console.log(schedules);
    },
  );

  // Convert schedules from API to ScheduleEvent[]
  const events: ScheduleEvent[] = schedules
    ? schedules.map((item) => ({
        id: String(item.scheduleID),
        title: item.title,
        startTime: dayjs(item.startDate).format("HH:mm"),
        endTime: dayjs(item.endDate).format("HH:mm"),
        type: "appointment" as const, // TODO: update logic if type is available
      }))
    : [];

  return (
    <ScheduleSectionContainer>
      <ScheduleHeader
        currentView={currentView}
        currentDate={currentDate}
        onViewChange={setCurrentView}
        onNavigateDate={navigateDate}
      />
      <BigCalendarView
        currentDate={currentDate}
        currentView={currentView}
        onNavigate={handleDateChange}
        onView={setCurrentView}
        events={events}
      />
    </ScheduleSectionContainer>
  );
};
