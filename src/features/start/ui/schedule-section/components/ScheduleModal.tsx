import React from "react";

import { Controller, useForm } from "react-hook-form";
import styled from "styled-components";
import dayjs from "dayjs";

import { Modal } from "@/components/ui/Modal";
import { Button } from "@/components/ui/NewButton";
import { TextInput } from "@/components/ui/TextInput";
import { DatePicker } from "@/components/ui/DatePicker";
import { Checkbox } from "@/components/ui/Checkbox";
import { InputLabel } from "@/components/ui/InputLabel";
import { SvgIconCalendar } from "@/components/ui/Icon/IconCalendar";

import type { ScheduleModalProps, ScheduleFormData } from "../types";

const ModalContent = styled.div`
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const FormField = styled.div`
  display: flex;
  flex-direction: column;
`;

const DateRangeContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const StyledDatePicker = styled(DatePicker)`
  width: 120px;
`;

const CheckboxContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const CheckboxIcon = styled.div`
  width: 20px;
  height: 20px;
  background-color: #43c3d5;
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;

  &::after {
    content: "✓";
    color: white;
    font-size: 12px;
    font-weight: bold;
  }
`;

const CommentTextArea = styled.textarea`
  width: 100%;
  min-height: 120px;
  padding: 12px;
  border: 1px solid #e2e3e5;
  border-radius: 6px;
  background-color: #fbfcfe;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;

  &:focus {
    outline: none;
    border-color: #4ebbe0;
  }

  &::placeholder {
    color: #a2aeb8;
  }
`;

const DeleteButton = styled.button`
  background: none;
  border: none;
  color: #e74c3c;
  font-size: 14px;
  cursor: pointer;
  text-align: right;
  padding: 0;
  margin-top: 8px;

  &:hover {
    text-decoration: underline;
  }
`;

const StyledInputLabel = styled(InputLabel)`
  font-size: 14px;
  font-weight: 500;
  color: #243544;
`;

export const ScheduleModal: React.FC<ScheduleModalProps> = ({
  isOpen,
  onClose,
  onSave,
  onDelete,
  initialData,
  selectedDate,
}) => {
  console.log("selectedDate", selectedDate);
  const { control, handleSubmit } = useForm<ScheduleFormData>({
    defaultValues: {
      title: initialData?.title || "",
      startDate: selectedDate || dayjs(),
      endDate: selectedDate || dayjs(),
      isAllDay: initialData?.isAllDay || false,
      comment: initialData?.comment || "",
    },
  });

  const onSubmit = (data: ScheduleFormData) => {
    onSave(data);
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      title="スケジュール"
      width={480}
      onCancel={onClose}
      footer={[
        <Button key="cancel" varient="tertiary" shape="round" onClick={onClose}>
          キャンセル
        </Button>,
        <Button
          key="save"
          varient="primary"
          shape="round"
          onClick={handleSubmit(onSubmit)}
        >
          保存
        </Button>,
      ]}
    >
      <ModalContent>
        <FormField>
          <StyledInputLabel label="タイトル" />
          <Controller
            name="title"
            control={control}
            render={({ field }) => (
              <TextInput {...field} placeholder="院長休み" />
            )}
          />
        </FormField>

        <FormField>
          <StyledInputLabel label="期間" />
          <DateRangeContainer>
            <Controller
              control={control}
              name="startDate"
              render={({ field }) => (
                <StyledDatePicker
                  suffixIcon={<SvgIconCalendar />}
                  allowClear={false}
                  {...field}
                />
              )}
            />

            <span>〜</span>

            <Controller
              control={control}
              name="endDate"
              render={({ field }) => (
                <StyledDatePicker
                  suffixIcon={<SvgIconCalendar />}
                  allowClear={false}
                  {...field}
                />
              )}
            />
          </DateRangeContainer>
        </FormField>

        <CheckboxContainer>
          <Controller
            name="isAllDay"
            control={control}
            render={({ field: { value, onChange } }) => (
              <>
                {value ? (
                  <CheckboxIcon />
                ) : (
                  <Checkbox
                    checked={value}
                    onChange={(e) => onChange(e.target.checked)}
                  />
                )}
                <span>終日</span>
              </>
            )}
          />
        </CheckboxContainer>

        <FormField>
          <StyledInputLabel label="コメント" />
          <Controller
            name="comment"
            control={control}
            render={({ field }) => (
              <CommentTextArea {...field} placeholder="" />
            )}
          />
        </FormField>

        {onDelete && (
          <DeleteButton onClick={onDelete}>スケジュールを削除</DeleteButton>
        )}
      </ModalContent>
    </Modal>
  );
};
