import React from "react";

import styled from "styled-components";

import { SvgIconCalendarArrowLeft } from "@/components/ui/Icon/IconCalendarArrowLeft";
import { SvgIconCalendarArrowRight } from "@/components/ui/Icon/IconCalendarArrowRight";
import { formatText } from "@/features/start/utils";

import type { DateNavigationProps } from "../types";

const SelectedDateSection = styled.div`
  display: flex;
  align-items: center;
  column-gap: 12px;
`;

const DateSelected = styled.div`
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  color: #243544;
  .number {
    font-family: Roboto;
  }
`;

const StyledButton = styled.button<{ $width?: number; $height?: number }>`
  width: ${({ $width }) => ($width ? `${$width}px` : "28px")};
  height: ${({ $height }) => ($height ? `${$height}px` : "28px")};
  padding: 0;
  border: 1px solid #e2e3e5;
  border-radius: 4px;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  &:hover {
    background-color: #f8f9fa;
    border-color: #d1d5db;
  }
  &:active {
    background-color: #e5e7eb;
  }
`;

export const DateNavigation: React.FC<DateNavigationProps> = ({
  currentDate,
  currentView,
  onNavigate,
}) => {
  const formatDate = () => {
    if (currentView === "day") {
      return formatText(currentDate.format("YYYY年MM月DD日"));
    } else {
      return formatText(currentDate.format("YYYY年MM月"));
    }
  };

  return (
    <SelectedDateSection>
      <StyledButton $height={28} $width={28} onClick={() => onNavigate("prev")}>
        <SvgIconCalendarArrowLeft />
      </StyledButton>
      <DateSelected>{formatDate()}</DateSelected>
      <StyledButton $height={28} $width={28} onClick={() => onNavigate("next")}>
        <SvgIconCalendarArrowRight />
      </StyledButton>
    </SelectedDateSection>
  );
};
