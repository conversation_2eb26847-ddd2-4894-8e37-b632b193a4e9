import React, { useMemo, useState } from "react";

import { Calendar, momentLocalizer } from "react-big-calendar";
import "react-big-calendar/lib/css/react-big-calendar.css";
import moment from "moment";
import "moment/locale/ja";
import styled from "styled-components";
import dayjs from "dayjs";

import { ScheduleModal } from "./ScheduleModal";

import type { Dayjs } from "dayjs";
import type {
  BigCalendarEvent,
  ScheduleEvent,
  ViewType,
  ScheduleFormData,
} from "../types";
import type { View } from "react-big-calendar";

// Set moment locale to Japanese
moment.locale("ja");
const localizer = momentLocalizer(moment);

const CalendarContainer = styled.div`
  flex: 1;
  height: 100%;
  overflow: hidden;

  .rbc-calendar {
    height: 100%;
    font-family: inherit;
  }

  /* Hide the default react-big-calendar toolbar */
  .rbc-toolbar {
    display: none;
  }

  .rbc-header {
    background-color: #fff;
    border: none;
    padding: 8px;
    font-size: 14px;
    font-weight: normal;
    color: #243544;
  }

  .rbc-month-view {
    border: none;
    border-bottom: 1px solid #e2e3e5;
    overflow: hidden;
  }

  .rbc-month-row {
    border-left: 1px solid #e2e3e5;
    border-right: 1px solid #e2e3e5;
  }

  .rbc-month-header {
    border-bottom: 1px solid #e2e3e5;
  }

  .rbc-date-cell {
    text-align: center;
    font-weight: normal;
    font-size: 14px;
    padding: 4px;
    height: 54px;
  }

  .rbc-off-range-bg {
    background-color: #f5f5f5;
  }

  .rbc-today {
    background-color: #e3f2fd;
  }

  .rbc-event {
    border-radius: 4px;
    border: none;
    padding: 2px 4px;
    font-size: 12px;
    font-weight: 500;
  }

  .rbc-event.appointment {
    background-color: #4ebbe0;
    color: white;
  }

  .rbc-event.examination {
    background-color: #52c41a;
    color: white;
  }

  .rbc-event.break {
    background-color: #999;
    color: white;
  }

  .rbc-day-slot .rbc-time-slot {
    border-top: 1px solid #f0f0f0;
  }

  .rbc-time-view {
    border: 1px solid #e2e3e5;
    border-radius: 8px;
    overflow: hidden;
  }

  .rbc-time-header {
    display: none !important;
  }

  .rbc-time-content {
    border-top: none;
  }

  .rbc-time-gutter {
    background-color: #f8f9fa;
    border-right: 1px solid #e2e3e5;
  }

  .rbc-timeslot-group {
    border-bottom: 1px solid #f0f0f0;
  }

  .rbc-time-slot {
    border-top: 1px solid #f8f8f8;
  }

  .rbc-current-time-indicator {
    background-color: #ff4757;
    height: 2px;
    z-index: 3;
  }

  .rbc-agenda-view {
    border: 1px solid #e2e3e5;
    border-radius: 8px;
    overflow: hidden;
  }

  .rbc-agenda-table {
    font-size: 14px;
  }

  .rbc-agenda-date-cell,
  .rbc-agenda-time-cell {
    padding: 8px;
    border-bottom: 1px solid #f0f0f0;
  }

  .rbc-agenda-event-cell {
    padding: 8px;
    border-bottom: 1px solid #f0f0f0;
  }

  .rbc-off-range {
    /* Đã loại bỏ visibility: hidden !important; */
  }

  .rbc-month-view .rbc-off-range {
    position: relative;
    color: transparent !important;
  }
  .rbc-month-view .rbc-off-range::after {
    content: "";
    position: absolute;
    inset: 0;
    background: #dde1e8;
    z-index: 2;
  }
`;

type BigCalendarViewProps = {
  currentDate: Dayjs;
  currentView: ViewType;
  events?: ScheduleEvent[];
  onNavigate?: (date: Date) => void;
  onView?: (view: ViewType) => void;
};

// Sample events for demonstration
const sampleEvents: ScheduleEvent[] = [
  {
    id: "1",
    title: "定期検診",
    startTime: "09:00",
    endTime: "10:00",
    type: "examination",
  },
  {
    id: "2",
    title: "診察予約",
    startTime: "14:00",
    endTime: "15:00",
    type: "appointment",
  },
  {
    id: "3",
    title: "休憩",
    startTime: "12:00",
    endTime: "13:00",
    type: "break",
  },
];

export const BigCalendarView: React.FC<BigCalendarViewProps> = ({
  currentDate,
  currentView,
  events = sampleEvents,
  onNavigate,
  onView: _onView,
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Dayjs | null>(null);

  // Handle date cell click - always show modal, prevent navigation
  const handleSelectSlot = ({ start }: { start: Date }) => {
    console.log("start", dayjs(start));
    setSelectedDate(dayjs(start));
    setIsModalOpen(true);
  };

  // Override navigation to prevent automatic view changes when clicking date cells
  const handleNavigate = (date: Date) => {
    console.log("date", dayjs(date));
    // Only allow navigation through header controls, not date cell clicks
    if (onNavigate) {
      onNavigate(date);
    }
    setSelectedDate(dayjs(date));
    setIsModalOpen(true);
  };

  // Override view change to prevent automatic view changes when clicking date cells
  const handleViewChange = (_view: View) => {
    // Prevent automatic view changes - only allow through header controls
    // Don't call onView to prevent switching to day view when clicking date cells
    return;
  };

  // Handle modal save
  const handleSave = (_data: ScheduleFormData) => {
    // TODO: Implement save logic
  };

  // Handle modal close
  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedDate(null);
  };

  // Convert ScheduleEvent to BigCalendarEvent
  const calendarEvents: BigCalendarEvent[] = useMemo(() => {
    return events.map((event) => {
      const eventDate = currentDate.toDate();
      const [startHour, startMinute] = event.startTime.split(":").map(Number);
      const [endHour, endMinute] = event.endTime.split(":").map(Number);

      const start = new Date(eventDate);
      start.setHours(startHour || 0, startMinute || 0, 0, 0);

      const end = new Date(eventDate);
      end.setHours(endHour || 0, endMinute || 0, 0, 0);

      return {
        id: event.id,
        title: event.title,
        start,
        end,
        resource: {
          type: event.type,
        },
      };
    });
  }, [events, currentDate]);

  // Convert ViewType to react-big-calendar view (only day and month)
  const getCalendarView = (view: ViewType): View => {
    switch (view) {
      case "day":
        return "day";
      case "month":
        return "month";
      default:
        return "month";
    }
  };

  // Custom event style getter
  const eventStyleGetter = (event: BigCalendarEvent) => {
    const type = event.resource?.type || "appointment";
    return {
      className: type,
    };
  };

  // Japanese messages for react-big-calendar
  const messages = {
    allDay: "終日",
    previous: "前",
    next: "次",
    today: "今日",
    month: "月",
    week: "週",
    day: "日",
    agenda: "予定",
    date: "日付",
    time: "時間",
    event: "イベント",
    noEventsInRange: "この期間にイベントはありません",
    showMore: (total: number) => `+${total} 件`,
  };

  return (
    <>
      <CalendarContainer>
        <Calendar
          localizer={localizer}
          events={calendarEvents}
          startAccessor="start"
          endAccessor="end"
          view={getCalendarView(currentView)}
          views={["day", "month"]} // Only allow day and month views
          onNavigate={handleNavigate}
          onView={handleViewChange}
          onSelectSlot={handleSelectSlot}
          selectable={true}
          eventPropGetter={eventStyleGetter}
          // messages={messages}
          popup={false} // Disable popup to ensure slot selection works
          // formats={{
          //   monthHeaderFormat: "YYYY年M月",
          //   dayHeaderFormat: "M月D日(ddd)",
          //   dayRangeHeaderFormat: ({
          //     start,
          //     end,
          //   }: {
          //     start: Date;
          //     end: Date;
          //   }) =>
          //     `${moment(start).format("M月D日")} - ${moment(end).format("M月D日")}`,
          //   agendaHeaderFormat: ({ start, end }: { start: Date; end: Date }) =>
          //     `${moment(start).format("M月D日")} - ${moment(end).format("M月D日")}`,
          // }}
          // step={30}
          // timeslots={2}
          // showMultiDayTimes={true}
        />
      </CalendarContainer>

      <ScheduleModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onSave={handleSave}
        selectedDate={selectedDate || undefined}
      />
    </>
  );
};
