import React, { useState } from "react";

import dynamic from "next/dynamic";
import Link from "next/link";
import styled from "styled-components";
import { Row } from "antd";

import { SvgIconTestCollaborationWhite } from "@/components/ui/Icon/IconTestCollaborationWhite";
import { SvgIconReceiptWhite } from "@/components/ui/Icon/IconReceiptWhite";
import { SvgIconPrintoutWhite } from "@/components/ui/Icon/IconPrintoutWhite";
import ClinicMapLogo from "@/components/ui/Icon/ClinicMapLogo";
import { SvgIconPatientListGray } from "@/components/ui/Icon/IconPatientListGray";
import { usePatientSearch } from "@/hooks/usePatientSearchForm";
import { usePatientContext } from "@/components/common/Patient/AddPatient/Providers/PatientProvider";
import { SvgIconLogo } from "@/components/ui/Icon/IconLogo";

const PatientHeaderSearchModal = dynamic(() =>
  import(
    "@/components/common/RootLayout/ClinicLayout/PatientHeaderSearchModal"
  ).then((mod) => mod.PatientHeaderSearchModal),
);

// Main container for the start page sidebar
const SidebarContainer = styled.div`
  height: calc(100vh - 68px);
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 40px;
  padding: 20px;
  background-color: #005bac;
  border-top-right-radius: 12px;
`;

// Base button styles
const BaseNavigationButton = styled(Link)`
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  transition: background-color 0.2s ease;
`;

// Primary large navigation button (for main features)
const PrimaryNavigationButton = styled(BaseNavigationButton)`
  width: 100%;
  height: 48px;
  background-color: white;
  border-radius: 10px;
  font-size: 18px;
  font-weight: bold;
  color: #333;

  &:hover {
    background-color: #f5f5f5;
    color: #333;
  }

  svg {
    height: 32px;
  }
`;

// Secondary small navigation button (for grouped features)
const SecondaryNavigationButton = styled(BaseNavigationButton)`
  flex: 1;
  height: 48px;
  background-color: #fff;
  border-radius: 8px;
  font-size: 14px;
  color: #243544;
  letter-spacing: -1px;

  &:hover {
    background-color: #f5f5f5;
    color: #243544;
  }

  span {
    line-height: 1;
  }
`;

// Blue action button (for management features)
const ActionButton = styled(BaseNavigationButton)`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  flex: 1;
  height: 40px;
  font-size: 14px;
  background-color: #329ce7;
  border-radius: 8px;
  color: #fff;

  &:hover {
    background-color: #2a8bc7;
    color: #fff;
  }

  span {
    line-height: 1;
  }
`;

// Special patient list button with icon
const PatientListNavigationButton = styled.button`
  width: 208px;
  height: 40px;
  background-color: #329ce7;
  border-radius: 8px;
  color: white;
  overflow: hidden;
  border: none;
  display: flex;
  align-items: center;
  cursor: pointer;
  &:hover {
    background-color: #2a8bc7;
    color: white;
  }
`;

const PatientListIcon = styled.div`
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px 8px;
  background-color: #fff;
`;

const PatientListLabel = styled.span`
  flex: 1;
  text-align: center;
  font-size: 14px;

  span {
    line-height: 1;
  }
`;

// New registration button with special styling
const NewRegistrationButton = styled.button`
  width: 120px;
  height: 36px;
  border-radius: 24px;
  background-color: #43c3d5;
  color: #fff;
  font-size: 14px;
  border: none;
  cursor: pointer;

  &:hover {
    background-color: #3ab0c1;
    color: #fff;
  }

  span {
    line-height: 1;
  }
`;

// Layout containers for button groups
const ButtonGroupRow = styled.div`
  width: 100%;
  display: flex;
  gap: 20px;
`;

const PatientManagementRow = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

// Main component with improved naming and structure
export const StartPageSidebar = () => {
  const [isPatientModalOpen, setIsPatientModalOpen] = useState(false);
  const {
    control,
    loading,
    onSubmit,
    onFetchMore,
    onForceFetchMore,
    patients,
    handleResetValue,
    handleSetValue,
  } = usePatientSearch();
  const { handleOpenModal, handleSetPatientProps } = usePatientContext();

  const handleOpenPatientModal = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsPatientModalOpen(true);
  };
  const handleClosePatientModal = () => {
    setIsPatientModalOpen(false);
    handleResetValue();
  };

  return (
    <SidebarContainer>
      {/* Primary navigation section */}
      <Row gutter={[0, 20]}>
        {/* Main clinic map feature */}
        <PrimaryNavigationButton href="/clinic-map">
          <ClinicMapLogo />
        </PrimaryNavigationButton>

        {/* Digital health services group */}
        <ButtonGroupRow>
          <SecondaryNavigationButton href="/online-consultation">
            <span>オンライン診療</span>
          </SecondaryNavigationButton>
          <SecondaryNavigationButton href="/web-inquiry">
            <span>WEB問診</span>
          </SecondaryNavigationButton>
          <SecondaryNavigationButton href="/ai-assist">
            <span>AIアシスト</span>
          </SecondaryNavigationButton>
        </ButtonGroupRow>

        {/* AI Chart feature */}
        <PrimaryNavigationButton href="/reception">
          <SvgIconLogo />
        </PrimaryNavigationButton>
      </Row>

      {/* Management and administrative section */}
      <Row gutter={[0, 20]}>
        {/* Patient management */}
        <PatientManagementRow>
          <PatientListNavigationButton
            type="button"
            onClick={handleOpenPatientModal}
          >
            <PatientListIcon>
              <SvgIconPatientListGray />
            </PatientListIcon>
            <PatientListLabel>
              <span>患者リスト</span>
            </PatientListLabel>
          </PatientListNavigationButton>
          <NewRegistrationButton
            type="button"
            onClick={() => {
              handleSetPatientProps({ initialStep: "ONLY_ADD_PATIENT" });
              handleOpenModal("NEW_PATIENT");
            }}
          >
            <span>新規登録</span>
          </NewRegistrationButton>
        </PatientManagementRow>

        {/* Reception and reservation management */}
        <ButtonGroupRow>
          <ActionButton href="/reception">
            <span>受付一覧</span>
          </ActionButton>
          <ActionButton href="/calendar">
            <span>予約管理</span>
          </ActionButton>
        </ButtonGroupRow>

        {/* Laboratory, receipt, and report management */}
        <ButtonGroupRow>
          <ActionButton href="/request-exam">
            <SvgIconTestCollaborationWhite />
            <span>検査連携</span>
          </ActionButton>
          <ActionButton href="/receipts">
            <SvgIconReceiptWhite />
            <span>レセプト</span>
          </ActionButton>
          <ActionButton href="/setting/ledger">
            <SvgIconPrintoutWhite />
            <span>帳票印刷</span>
          </ActionButton>
        </ButtonGroupRow>
      </Row>
      <PatientHeaderSearchModal
        isOpen={isPatientModalOpen}
        onClose={handleClosePatientModal}
        loading={loading}
        control={control}
        onSubmit={onSubmit}
        onFetchMore={onFetchMore}
        onForceFetchMore={onForceFetchMore}
        patients={patients}
        handleSetValue={handleSetValue}
        handleResetValue={handleResetValue}
      />
    </SidebarContainer>
  );
};

// Export with original name for backward compatibility
export const LeftContent = StartPageSidebar;
