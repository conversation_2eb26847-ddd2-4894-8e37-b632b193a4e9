import React, { useState, useEffect } from "react";

import styled from "styled-components";

import { Modal } from "@/components/ui/Modal";
import { Button } from "@/components/ui/NewButton";
import { TextAreaInput } from "@/components/ui/TextAreaInput";

const ModalWrapper = styled.div`
  padding: 20px 24px;
`;

const StyledTextArea = styled(TextAreaInput)`
  width: 100%;
  min-height: 300px;
  font-size: 14px;
  line-height: 1.5;
  font-family: "NotoSansJP", sans-serif;
  resize: none;
  border-radius: 8px;
  border: 1px solid #e2e3e5;
  background-color: #fbfcfe;

  &:focus {
    border-color: #4ebbe0;
    outline: none;
  }
`;

const StyledButton = styled(Button)<{ $isCancel?: boolean }>`
  width: 120px;
  height: 40px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  font-family: "NotoSansJP", sans-serif;

  ${({ $isCancel }) =>
    $isCancel &&
    `
    background-color: #e2e3e5 !important;
    color: #6a757d !important;
    border: none !important;

    &:hover {
      background-color: #d1d3d5 !important;
      opacity: 1 !important;
    }
  `}
`;

type Props = {
  isOpen: boolean;
  initialContent: string;
  onClose: () => void;
  onSave: (content: string) => void;
};

export const HospitalBoardEditModal: React.FC<Props> = ({
  isOpen,
  initialContent,
  onClose,
  onSave,
}) => {
  const [content, setContent] = useState(initialContent);

  // Update content when modal opens with new initial content
  useEffect(() => {
    if (isOpen) {
      setContent(initialContent);
    }
  }, [isOpen, initialContent]);

  const handleSave = () => {
    onSave(content);
    onClose();
  };

  const handleCancel = () => {
    onClose();
  };

  return (
    <Modal
      title="院内掲示板"
      isOpen={isOpen}
      onCancel={handleCancel}
      width={600}
      centered
      footer={[
        <StyledButton
          key="cancel"
          varient="tertiary"
          $isCancel={true}
          onClick={handleCancel}
        >
          キャンセル
        </StyledButton>,
        <StyledButton key="save" varient="primary" onClick={handleSave}>
          保存
        </StyledButton>,
      ]}
    >
      <ModalWrapper>
        <StyledTextArea
          value={content}
          onChange={(e) => setContent(e.target.value)}
          placeholder="院内掲示板の内容を入力してください"
        />
      </ModalWrapper>
    </Modal>
  );
};
