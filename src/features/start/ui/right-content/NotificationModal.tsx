import React, { useState } from "react";

import styled from "styled-components";

import { Modal } from "@/components/ui/Modal";
import { Button } from "@/components/ui/NewButton";

interface NotificationData {
  id: number;
  date: string;
  type: "important" | "service" | "campaign";
  typeLabel: string;
  title: string;
  content?: string;
  details?: {
    description: string;
    incidentOverview?: string;
    timeRange?: string;
    contactInfo?: string;
    contactUrl?: string;
  };
}

interface NotificationModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const ModalContent = styled.div`
  display: flex;
  height: 600px;
  background-color: #fff;
  border-radius: 0 0 12px 12px;
`;

const Sidebar = styled.div`
  width: 380px;
  border-right: 1px solid #e2e3e5;
  display: flex;
  flex-direction: column;
`;

const SidebarHeader = styled.div`
  padding: 16px 20px;
  border-bottom: 1px solid #e2e3e5;
  background-color: #f8f9fa;
`;

const SelectAllCheckbox = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #243544;
  font-family: "NotoSansJP";
`;

const NotificationList = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 0;
`;

const NotificationItem = styled.div<{ $isSelected: boolean }>`
  padding: 16px 20px;
  border-bottom: 1px solid #e2e3e5;
  cursor: pointer;
  background-color: ${({ $isSelected }) => ($isSelected ? "#f0f8ff" : "#fff")};

  &:hover {
    background-color: #f8f9fa;
  }
`;

const NotificationItemHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
`;

const Checkbox = styled.input`
  width: 16px;
  height: 16px;
  accent-color: #007aff;
`;

const DateText = styled.span`
  color: #6a757d;
  font-size: 14px;
  font-family: "Roboto";
  line-height: 1;
`;

const Badge = styled.span<{ $type: "important" | "service" | "campaign" }>`
  height: 20px;
  font-size: 12px;
  color: #fff;
  padding: 0 16px;
  border-radius: 2px;
  font-family: "NotoSansJP";
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;

  ${({ $type }) => {
    switch ($type) {
      case "important":
        return `background-color: #e74c3c;`;
      case "service":
        return `background-color: #27ae60;`;
      case "campaign":
        return `background-color: #f39c12;`;
      default:
        return `background-color: #999;`;
    }
  }}
`;

const NotificationTitle = styled.div`
  color: #243544;
  font-size: 14px;
  line-height: 1.4;
  font-weight: bold;
  font-family: "NotoSansJP";
`;

const DetailArea = styled.div`
  flex: 1;
  padding: 20px;
  overflow-y: auto;
`;

const DetailTitle = styled.h2`
  font-size: 18px;
  font-weight: bold;
  color: #243544;
  margin: 0 0 16px 0;
  font-family: "NotoSansJP";
  line-height: 1.4;
`;

const DetailContent = styled.div`
  font-size: 14px;
  color: #243544;
  line-height: 1.6;
  font-family: "NotoSansJP";

  p {
    margin: 0 0 16px 0;
  }

  h3 {
    font-size: 16px;
    font-weight: bold;
    margin: 24px 0 8px 0;
    color: #243544;
  }

  a {
    color: #007aff;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
`;

const ModalFooter = styled.div`
  display: flex;
  justify-content: center;
  padding: 20px;
  border-top: 1px solid #e2e3e5;
  background-color: #fff;
`;

// Sample detailed notification data
const detailedNotifications: NotificationData[] = [
  {
    id: 1,
    date: "2024/05/25",
    type: "service",
    typeLabel: "サービス改善",
    title: "メンテナンスのお知らせ",
    details: {
      description: "システムメンテナンスを実施いたします。",
    },
  },
  {
    id: 2,
    date: "2024/05/24",
    type: "important",
    typeLabel: "重要",
    title: "システム障害に関するお詫び",
    details: {
      description:
        "2024年5月23日(木)に発生しましたシステム障害により、弊社サービスをご利用いただいたお客さまに多大なご迷惑をおかけしましたことを深くお詫び申し上げます。\n\n不具合につきましては、既に復旧しております。\n\n今後は再発防止に努めてまいりますので、引き続きご愛顧を賜りますようお願い申し上げます。",
      incidentOverview:
        "以下の時間帯において、システム障害により、弊社サービスへのアクセス時にエラー画面へ遷移し、問題できない状況が発生しておりました。",
      timeRange: "2024年5月24日(木)15時00分 ～ 26時05分",
      contactInfo: "以下のリンクからご確認ください。",
      contactUrl: "https://gmo-hc.com/contact",
    },
  },
  {
    id: 3,
    date: "2024/04/25",
    type: "service",
    typeLabel: "サービス改善",
    title: "ゴールデンウィーク期間中のお問い合わせ対応について",
    details: {
      description:
        "ゴールデンウィーク期間中のお問い合わせ対応についてお知らせいたします。",
    },
  },
  {
    id: 4,
    date: "2024/04/20",
    type: "campaign",
    typeLabel: "キャンペーン",
    title: "2024年6月31日まで初期費用10万円割引キャンペーンを実施",
    details: {
      description:
        "2024年6月31日まで初期費用10万円割引キャンペーンを実施しております。",
    },
  },
  {
    id: 5,
    date: "2024/03/18",
    type: "service",
    typeLabel: "サービス改善",
    title: "令和6年度診療報酬改定に関するバージョンアップのお知らせ",
    details: {
      description:
        "令和6年度診療報酬改定に関するバージョンアップを実施いたします。",
    },
  },
  {
    id: 6,
    date: "2024/02/16",
    type: "important",
    typeLabel: "重要",
    title: "システムメンテナンスのお知らせ",
    details: {
      description: "システムメンテナンスを実施いたします。",
    },
  },
];

export const NotificationModal: React.FC<NotificationModalProps> = ({
  isOpen,
  onClose,
}) => {
  const [selectedNotifications, setSelectedNotifications] = useState<number[]>(
    [],
  );
  const [selectedNotificationId, setSelectedNotificationId] = useState<
    number | null
  >(detailedNotifications[1]?.id || null);
  const [selectAll, setSelectAll] = useState(false);

  const handleCheckboxChange = (id: number) => {
    setSelectedNotifications((prev) =>
      prev.includes(id)
        ? prev.filter((notifId) => notifId !== id)
        : [...prev, id],
    );
  };

  const handleSelectAllChange = () => {
    if (selectAll) {
      setSelectedNotifications([]);
    } else {
      setSelectedNotifications(detailedNotifications.map((n) => n.id));
    }
    setSelectAll(!selectAll);
  };

  const handleNotificationClick = (id: number) => {
    setSelectedNotificationId(id);
  };

  const selectedNotification = detailedNotifications.find(
    (n) => n.id === selectedNotificationId,
  );

  return (
    <Modal
      isOpen={isOpen}
      onCancel={onClose}
      title="お知らせ"
      width={1000}
      footer={null}
      destroyOnClose
    >
      <ModalContent>
        <Sidebar>
          <SidebarHeader>
            <SelectAllCheckbox>
              <Checkbox
                type="checkbox"
                checked={selectAll}
                onChange={handleSelectAllChange}
              />
              選択したお知らせを既読にする
            </SelectAllCheckbox>
          </SidebarHeader>
          <NotificationList>
            {detailedNotifications.map((notification) => (
              <NotificationItem
                key={notification.id}
                $isSelected={notification.id === selectedNotificationId}
                onClick={() => handleNotificationClick(notification.id)}
              >
                <NotificationItemHeader>
                  <Checkbox
                    type="checkbox"
                    checked={selectedNotifications.includes(notification.id)}
                    onChange={() => handleCheckboxChange(notification.id)}
                    onClick={(e) => e.stopPropagation()}
                  />
                  <DateText>{notification.date}</DateText>
                  <Badge $type={notification.type}>
                    {notification.typeLabel}
                  </Badge>
                  {notification.id === 2 && (
                    <Badge $type="service">サービス改善</Badge>
                  )}
                </NotificationItemHeader>
                <NotificationTitle>{notification.title}</NotificationTitle>
              </NotificationItem>
            ))}
          </NotificationList>
        </Sidebar>

        <DetailArea>
          {selectedNotification && (
            <>
              <DetailTitle>{selectedNotification.title}</DetailTitle>
              <DetailContent>
                <p>{selectedNotification.details?.description}</p>

                {selectedNotification.details?.incidentOverview && (
                  <>
                    <h3>【障害の概要】</h3>
                    <p>{selectedNotification.details.incidentOverview}</p>
                  </>
                )}

                {selectedNotification.details?.timeRange && (
                  <p>{selectedNotification.details.timeRange}</p>
                )}

                {selectedNotification.details?.contactInfo && (
                  <>
                    <h3>【お問い合わせ先】</h3>
                    <p>{selectedNotification.details.contactInfo}</p>
                    {selectedNotification.details.contactUrl && (
                      <p>
                        <a
                          href={selectedNotification.details.contactUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          {selectedNotification.details.contactUrl}
                        </a>
                      </p>
                    )}
                  </>
                )}
              </DetailContent>
            </>
          )}
        </DetailArea>
      </ModalContent>

      <ModalFooter>
        <Button onClick={onClose} varient="secondary">
          閉じる
        </Button>
      </ModalFooter>
    </Modal>
  );
};
