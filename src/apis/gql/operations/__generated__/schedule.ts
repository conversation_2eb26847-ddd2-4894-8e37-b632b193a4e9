import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type GetScheduleListQueryVariables = Types.Exact<{
  input: Types.GetScheduleListInput;
}>;

export type GetScheduleListQuery = {
  __typename?: "query_root";
  getScheduleList: Array<{
    __typename?: "Schedule";
    startDate: string;
    endDate: string;
    scheduleID: number;
    isAllDay?: boolean;
    comment?: string;
  }>;
};

export type AddScheduleMutationVariables = Types.Exact<{
  startDate: Types.Scalars["DateTime"]["input"];
  endDate: Types.Scalars["DateTime"]["input"];
  title: Types.Scalars["String"]["input"];
  comment: Types.Scalars["String"]["input"];
  isAllDay: Types.Scalars["Boolean"]["input"];
}>;

export type AddScheduleMutation = {
  __typename?: "mutation_root";
  editSchedule: boolean;
};

export type EditScheduleMutationVariables = Types.Exact<{
  scheduleID: Types.Scalars["Int"]["input"];
  startDate: Types.Scalars["DateTime"]["input"];
  endDate: Types.Scalars["DateTime"]["input"];
  title: Types.Scalars["String"]["input"];
  comment: Types.Scalars["String"]["input"];
  isAllDay: Types.Scalars["Boolean"]["input"];
}>;

export type EditScheduleMutation = {
  __typename?: "mutation_root";
  editSchedule: boolean;
};

export const GetScheduleListDocument = gql`
  query getScheduleList($input: getScheduleListInput!) {
    getScheduleList(input: $input) {
      startDate
      endDate
      scheduleID
      isAllDay
      comment
    }
  }
`;

/**
 * __useGetScheduleListQuery__
 *
 * To run a query within a React component, call `useGetScheduleListQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetScheduleListQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetScheduleListQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useGetScheduleListQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetScheduleListQuery,
    GetScheduleListQueryVariables
  > &
    (
      | { variables: GetScheduleListQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<GetScheduleListQuery, GetScheduleListQueryVariables>(
    GetScheduleListDocument,
    options,
  );
}
export function useGetScheduleListLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetScheduleListQuery,
    GetScheduleListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetScheduleListQuery,
    GetScheduleListQueryVariables
  >(GetScheduleListDocument, options);
}
export function useGetScheduleListSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetScheduleListQuery,
    GetScheduleListQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetScheduleListQuery,
    GetScheduleListQueryVariables
  >(GetScheduleListDocument, options);
}
export type GetScheduleListQueryHookResult = ReturnType<
  typeof useGetScheduleListQuery
>;
export type GetScheduleListLazyQueryHookResult = ReturnType<
  typeof useGetScheduleListLazyQuery
>;
export type GetScheduleListSuspenseQueryHookResult = ReturnType<
  typeof useGetScheduleListSuspenseQuery
>;
export type GetScheduleListQueryResult = Apollo.QueryResult<
  GetScheduleListQuery,
  GetScheduleListQueryVariables
>;
export const AddScheduleDocument = gql`
  mutation addSchedule(
    $startDate: DateTime!
    $endDate: DateTime!
    $title: String!
    $comment: String!
    $isAllDay: Boolean!
  ) {
    editSchedule(
      input: {
        startDate: $startDate
        endDate: $endDate
        title: $title
        comment: $comment
        isAllDay: $isAllDay
      }
    )
  }
`;
export type AddScheduleMutationFn = Apollo.MutationFunction<
  AddScheduleMutation,
  AddScheduleMutationVariables
>;

/**
 * __useAddScheduleMutation__
 *
 * To run a mutation, you first call `useAddScheduleMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useAddScheduleMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [addScheduleMutation, { data, loading, error }] = useAddScheduleMutation({
 *   variables: {
 *      startDate: // value for 'startDate'
 *      endDate: // value for 'endDate'
 *      title: // value for 'title'
 *      comment: // value for 'comment'
 *      isAllDay: // value for 'isAllDay'
 *   },
 * });
 */
export function useAddScheduleMutation(
  baseOptions?: Apollo.MutationHookOptions<
    AddScheduleMutation,
    AddScheduleMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<AddScheduleMutation, AddScheduleMutationVariables>(
    AddScheduleDocument,
    options,
  );
}
export type AddScheduleMutationHookResult = ReturnType<
  typeof useAddScheduleMutation
>;
export type AddScheduleMutationResult =
  Apollo.MutationResult<AddScheduleMutation>;
export type AddScheduleMutationOptions = Apollo.BaseMutationOptions<
  AddScheduleMutation,
  AddScheduleMutationVariables
>;
export const EditScheduleDocument = gql`
  mutation editSchedule(
    $scheduleID: Int!
    $startDate: DateTime!
    $endDate: DateTime!
    $title: String!
    $comment: String!
    $isAllDay: Boolean!
  ) {
    editSchedule(
      input: {
        scheduleID: $scheduleID
        startDate: $startDate
        endDate: $endDate
        title: $title
        comment: $comment
        isAllDay: $isAllDay
      }
    )
  }
`;
export type EditScheduleMutationFn = Apollo.MutationFunction<
  EditScheduleMutation,
  EditScheduleMutationVariables
>;

/**
 * __useEditScheduleMutation__
 *
 * To run a mutation, you first call `useEditScheduleMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useEditScheduleMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [editScheduleMutation, { data, loading, error }] = useEditScheduleMutation({
 *   variables: {
 *      scheduleID: // value for 'scheduleID'
 *      startDate: // value for 'startDate'
 *      endDate: // value for 'endDate'
 *      title: // value for 'title'
 *      comment: // value for 'comment'
 *      isAllDay: // value for 'isAllDay'
 *   },
 * });
 */
export function useEditScheduleMutation(
  baseOptions?: Apollo.MutationHookOptions<
    EditScheduleMutation,
    EditScheduleMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    EditScheduleMutation,
    EditScheduleMutationVariables
  >(EditScheduleDocument, options);
}
export type EditScheduleMutationHookResult = ReturnType<
  typeof useEditScheduleMutation
>;
export type EditScheduleMutationResult =
  Apollo.MutationResult<EditScheduleMutation>;
export type EditScheduleMutationOptions = Apollo.BaseMutationOptions<
  EditScheduleMutation,
  EditScheduleMutationVariables
>;
