import { gql } from "@apollo/client";

export const GET_SCHEDULE_LIST = gql`
  query getScheduleList($input: getScheduleListInput!) {
    getScheduleList(input: $input) {
      startDate
      endDate
      scheduleID
      isAllDay
      comment
    }
  }
`;

export const ADD_SCHEDULE = gql`
  mutation addSchedule(
    $startDate: DateTime!
    $endDate: DateTime!
    $title: String!
    $comment: String!
    $isAllDay: Boolean!
  ) {
    editSchedule(
      input: {
        startDate: $startDate
        endDate: $endDate
        title: $title
        comment: $comment
        isAllDay: $isAllDay
      }
    )
  }
`;

export const EDIT_SCHEDULE = gql`
  mutation editSchedule(
    $scheduleID: Int!
    $startDate: DateTime!
    $endDate: DateTime!
    $title: String!
    $comment: String!
    $isAllDay: Boolean!
  ) {
    editSchedule(
      input: {
        scheduleID: $scheduleID
        startDate: $startDate
        endDate: $endDate
        title: $title
        comment: $comment
        isAllDay: $isAllDay
      }
    )
  }
`;
