import { useState } from "react";

import dynamic from "next/dynamic";
import { Controller } from "react-hook-form";
import styled from "styled-components";

import { SvgIconSearch } from "@/components/ui/Icon/IconSearch";
import { TextInput } from "@/components/ui/TextInput";
import { usePatientSearch } from "@/hooks/usePatientSearchForm";

const PatientHeaderSearchModal = dynamic(() =>
  import("./PatientHeaderSearchModal").then(
    (mod) => mod.PatientHeaderSearchModal,
  ),
);

const SearchInput = styled(TextInput)`
  width: 240px;
  border-radius: 18px;
  height: 28px;

  &:hover {
    opacity: 1;
  }
`;

export const SearchWindow = () => {
  const [modalOpen, setModalOpen] = useState(false);

  const {
    loading,
    control,
    patients,
    onSubmit,
    onFetchMore,
    onForceFetchMore,
    handleSetValue,
    handleResetValue,
  } = usePatientSearch();

  const handleModalOpen = () => {
    setModalOpen(true);
  };

  const handleModalClose = () => {
    setModalOpen(false);
    handleResetValue();
  };

  return (
    <>
      <form
        onSubmit={(e) => {
          handleModalOpen();
          onSubmit(e);
        }}
      >
        <Controller
          name="searchKeyword"
          control={control}
          render={({ field }) => (
            <SearchInput
              {...field}
              placeholder="患者番号、氏名"
              prefix={<SvgIconSearch />}
            />
          )}
        />
      </form>

      <PatientHeaderSearchModal
        isOpen={modalOpen}
        onClose={handleModalClose}
        loading={loading}
        control={control}
        onSubmit={onSubmit}
        onFetchMore={onFetchMore}
        onForceFetchMore={onForceFetchMore}
        patients={patients}
        handleSetValue={handleSetValue}
        handleResetValue={handleResetValue}
      />
    </>
  );
};
