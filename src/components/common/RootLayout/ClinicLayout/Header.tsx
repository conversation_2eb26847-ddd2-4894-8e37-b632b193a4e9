import { Badge, Dropdown } from "antd";
import { useRouter } from "next/router";
import styled, { css } from "styled-components";

import { Header as CommonHeader } from "@/components/ui/Header";
import { SvgIconArrowPulldownWhite } from "@/components/ui/Icon/IconArrowPulldownWhite";
import { ZIndexOrder } from "@/constants/common";
import { headerMenuData } from "@/constants/url";
import { useHeaderLogic } from "@/hooks/useHeaderLogic";
import { GroupButton } from "@/components/ui/AIAssistButton";
import { SvgIconPatientList } from "@/components/ui/Icon/IconPatientList";
import {
  useZendeskSsoFormHandler,
  ZendeskSsoForm,
} from "@/components/common/Zendesk";
import { ZendeskFirstPage } from "@/constants/zendesk";

import { PasswordResetModal } from "../PasswordResetModal";
import { HPKILoginModal } from "../HPKILoginModal";

import { HeaderNotice } from "./HeaderNotice";
import { MailDeliveryModal } from "./MailDeliveryModal";
import { SearchWindow } from "./SearchWindow";
import { HeaderOtherMenu } from "./HeaderOtherMenu";
import { HeaderMessage } from "./HeaderMessage";

// Custom styled components for dropdown menu
const CustomDropdownMenu = styled.div`
  width: 160px;
  padding: 0;
  border-radius: 4px;
  box-shadow:
    0 2px 6px 2px rgba(0, 0, 0, 0.15),
    0 1px 2px 0 rgba(0, 0, 0, 0.3);
  background-color: #fff;
`;

const CustomMenuItem = styled.div<{ $isLogout?: boolean }>`
  height: 40px;
  padding: 0px 12px;
  cursor: pointer;
  display: flex;
  flex-direction: row;
  align-items: center;
  transition: background-color 0.2s ease;
  border-bottom: solid 1px #e2e3e5;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: #eaf0f5;
  }

  span {
    font-size: 14px;
    font-weight: normal;
    line-height: 1;
    color: ${({ $isLogout }) => ($isLogout ? "#e74c3c" : "#243544")};
  }
`;

const StyledMenu = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: ${ZIndexOrder.HeaderMenu};
  gap: 8px;
`;

const LeftMenu = styled.div`
  display: flex;
  align-items: center;
  margin-left: 12px;
`;

const RightMenu = styled.div`
  display: flex;
  align-items: center;
  margin-left: auto;
`;

const StyledMenuItem = styled.button<{ $active: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
  border: none;
  appearance: none;
  cursor: pointer;
  border-radius: 4px;
  height: 32px;
  background-color: #005bac;
  transition: 0.3s;
  white-space: nowrap;

  span {
    font-size: 14px;
    line-height: 1px;
    text-align: center;
    color: #ffffff;
  }

  ${({ $active }) => {
    if ($active) {
      return css`
        background-color: rgba(0, 0, 0, 0.3);
      `;
    }

    return css`
      &:hover,
      &:focus-visible {
        background-color: rgba(0, 0, 0, 0.2);
        box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
      }
    `;
  }}
`;

const StyledMenuPatientList = styled(StyledMenuItem)`
  gap: 4px;
  padding: 0px 8px;
  margin-left: 12px;
  svg {
    height: 27px;
  }
`;

const Divider = styled.div`
  height: 4px;
  background-color: #e0e6ec;
`;

const DropdownButton = styled.button`
  min-width: 160px;
  height: var(--global-header-height);
  width: auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  appearance: none;
  border: none;
  background-color: #005bac;

  &:focus-visible {
    transition: none;
    outline-offset: -4px;
  }
`;

const StaffName = styled.span`
  font-size: 14px;
  line-height: 14px;
  text-align: center;
  color: #ffffff;
  flex: 1;
`;

const ArrowIconWrapper = styled.div`
  flex: none;
  margin-top: 7px;
`;

export const Header: React.FC = () => {
  const {
    staffInfo,
    isOperator,
    agreements,
    isOpenHPKILoginModal,
    setIsOpenHPKILoginModal,
    isOpenPasswordResetModal,
    setIsOpenPasswordResetModal,
    isOpenMailSettingModal,
    setIsOpenMailSettingModal,
    messageBadgeVisible,
    handleMenuItemClick,
  } = useHeaderLogic();

  const router = useRouter();
  const { zendeskFormRef, sendZendeskForm } = useZendeskSsoFormHandler();

  return (
    <CommonHeader>
      <LeftMenu>
        <SearchWindow />
        <Badge>
          <StyledMenuPatientList $active={false}>
            <SvgIconPatientList />
            <span>患者リスト</span>
          </StyledMenuPatientList>
        </Badge>
      </LeftMenu>

      <RightMenu>
        <StyledMenu>
          <>
            {headerMenuData.map((menu) => (
              <Badge
                key={menu.pathname}
                offset={[-5, 5]}
                dot={menu.pathname === "/chat" && messageBadgeVisible}
              >
                <StyledMenuItem
                  key={menu.label}
                  onClick={() =>
                    handleMenuItemClick(`${menu.pathname}/${menu.query ?? ""}`)
                  }
                  $active={[menu.pathname, ...menu.relatedPaths].includes(
                    router.pathname,
                  )}
                >
                  <span>{menu.label}</span>
                </StyledMenuItem>
              </Badge>
            ))}
          </>
        </StyledMenu>

        <GroupButton type="CLINIC" />
        <HeaderOtherMenu />
        <HeaderNotice />
        <HeaderMessage />

        <Dropdown
          trigger={["click"]}
          placement="bottomRight"
          arrow={false}
          dropdownRender={() => (
            <CustomDropdownMenu>
              <CustomMenuItem onClick={() => setIsOpenHPKILoginModal(true)}>
                <span>HPKIログイン</span>
              </CustomMenuItem>
              <Divider />
              <CustomMenuItem onClick={() => router.push("/setting/hospital")}>
                <span>設定</span>
              </CustomMenuItem>
              <CustomMenuItem
                onClick={() => {
                  sendZendeskForm(ZendeskFirstPage.SUPPORT);
                }}
              >
                <span>FAQ</span>
              </CustomMenuItem>
              <Divider />
              <CustomMenuItem onClick={() => setIsOpenPasswordResetModal(true)}>
                <span>パスワード変更</span>
              </CustomMenuItem>
              <CustomMenuItem onClick={() => setIsOpenMailSettingModal(true)}>
                <span>メール配信設定</span>
              </CustomMenuItem>
              {agreements && agreements.length > 0 && (
                <CustomMenuItem onClick={() => router.push("/policy")}>
                  <span>ご確認・ご同意事項</span>
                </CustomMenuItem>
              )}
              <CustomMenuItem $isLogout onClick={() => router.push("/logout")}>
                <span>ログアウト</span>
              </CustomMenuItem>
            </CustomDropdownMenu>
          )}
        >
          <DropdownButton>
            <StaffName>
              {isOperator
                ? "ヘルステック担当者"
                : `${staffInfo?.staffName || ""} ${staffInfo?.staffType === 1 ? "医師" : ""}`}
            </StaffName>
            <ArrowIconWrapper>
              <SvgIconArrowPulldownWhite />
            </ArrowIconWrapper>
          </DropdownButton>
        </Dropdown>
        <ZendeskSsoForm ref={zendeskFormRef} />
      </RightMenu>

      <MailDeliveryModal
        isOpen={isOpenMailSettingModal}
        onClose={() => setIsOpenMailSettingModal(false)}
      />
      <PasswordResetModal
        isOpen={isOpenPasswordResetModal}
        onClose={() => setIsOpenPasswordResetModal(false)}
      />
      <HPKILoginModal
        isOpen={isOpenHPKILoginModal}
        onClose={() => setIsOpenHPKILoginModal(false)}
      />
    </CommonHeader>
  );
};
