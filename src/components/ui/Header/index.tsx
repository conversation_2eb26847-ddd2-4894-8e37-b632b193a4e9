import { useRouter } from "next/router";
import styled from "styled-components";

import { HeaderLogo } from "../HeaderLogo";

import type { ReactNode } from "react";

const StyledHeaderOther = styled.header`
  background-color: #005bac;
  display: flex;
  height: var(--global-header-height);
  padding-right: 12px;
  min-width: var(--application-min-width);
`;

const StyledHeaderSignup = styled.header`
  display: flex;
  height: 48px;
  background-color: #fbfcfe;
  justify-content: space-between;
  /* align-items: center; */

  /* padding: 0 12px; */
  padding: 15px 814.8px 15px 260px;
  @media (max-width: 600px) {
    align-items: center;
    justify-content: center;
    height: 48px;
    flex-grow: 0;
    padding: 15px 77.8px 15px 77px;
    margin: 0 auto;
    border-bottom: solid 1px #e2e3e5;
    background-color: #fbfcfe;
  }
`;

type Props = {
  children?: ReactNode;
  className?: string;
};

export const Header: React.FC<Props> = ({ children, className }) => {
  // サインアップとそれ以外でヘッダーを出し分ける
  const SIGNUP_PATHS = ["/signup", "/signup/sms"];
  const path = useRouter().pathname;
  const isSignup = SIGNUP_PATHS.includes(path);
  const StyledHeader = isSignup ? StyledHeaderSignup : StyledHeaderOther;

  return (
    <StyledHeader className={className}>
      <HeaderLogo />
      {children}
    </StyledHeader>
  );
};
