import React from "react";

import { useRouter } from "next/router";
import styled from "styled-components";

import { SvgIconLogoHealthtech } from "../Icon/IconLogoHealthtech";

const LogoContainer = styled.div`
  width: auto;
  background-color: transparent;
  display: flex;
  justify-content: center;
`;

const LogoHealthtechHeader = styled.div`
  width: 237px;
  height: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: 10px;
  padding: 0 20px 0 12px;
  background-color: #fff;
  border-bottom-right-radius: 20px;
`;

export const HeaderLogo: React.FC = () => {
  const SIGNUP_PATHS = ["/signup", "/signup/sms"];
  const path = useRouter().pathname;
  const isSignup = SIGNUP_PATHS.includes(path);
  // const Logo = isSignup ? <LogoHealthtechHeader>
  //   <SvgIconLogoHealthtech />
  // </LogoHealthtechHeader> : SvgIconLogoHealthtech;

  return (
    <LogoContainer>
      {!isSignup ? (
        <LogoHealthtechHeader>
          <SvgIconLogoHealthtech />
        </LogoHealthtechHeader>
      ) : (
        <SvgIconLogoHealthtech />
      )}
    </LogoContainer>
  );
};
