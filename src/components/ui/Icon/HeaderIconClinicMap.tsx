// ![DO NOT EDIT] this file is auto-generated by svgr;
import * as React from "react";
import type { SVGProps } from "react";
export const SvgHeaderIconClinicMap = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={24}
    height={24}
    fill="none"
    {...props}
  >
    <path
      fill="#F39939"
      d="M12.029 22C8.16 22 4 21.11 4 19.156c0-1.443 2.162-2.154 3.973-2.495a.647.647 0 0 1 .759.513.65.65 0 0 1-.517.754c-2.194.413-2.914 1.052-2.914 1.225 0 .362 2.097 1.55 6.728 1.55 4.63 0 6.727-1.191 6.727-1.55 0-.173-.715-.812-2.913-1.225a.643.643 0 0 1-.517-.754.646.646 0 0 1 .759-.513c1.811.34 3.973 1.052 3.973 2.495 0 1.954-4.161 2.844-8.03 2.844z"
    />
    <path
      fill="#F39939"
      d="M13.052 18.427c-.582.52-1.464.52-2.046 0-1.764-1.583-3.12-3.572-4.063-4.962q-1.681-2.469-1.681-4.57 0-3.151 2.042-5.024Q9.344 2.001 12.033 2c1.793 0 3.365.625 4.728 1.87q2.041 1.87 2.043 5.024 0 2.1-1.681 4.571c-.948 1.39-2.3 3.379-4.064 4.962zm-2.512-6.865v.761c0 .553.455 1.005 1.012 1.005h.958c.556 0 1.012-.452 1.012-1.005v-1.982h1.995c.557 0 1.013-.452 1.013-1.005v-.952c0-.553-.456-1.005-1.013-1.005h-1.995V5.397c0-.553-.456-1.006-1.012-1.006h-.958c-.557 0-1.012.453-1.012 1.006v1.982H8.544c-.557 0-1.012.452-1.012 1.005v.952c0 .553.455 1.005 1.012 1.005h1.996z"
    />
  </svg>
);
