import type { SVGProps } from "react";
import * as React from "react";
const ClinicMapLogo = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={234}
    height={24}
    viewBox="0 0 234 24"
    fill="none"
    {...props}
  >
    <g clipPath="url(#parts-logo_svg__a)">
      <path
        fill="#0757A0"
        d="M38.993 20.096h1.054a.2.2 0 0 0 .148-.082l4.377-7.115 2.383 8.14c.**************.125.09h4.59c.057 0 .087-.042.07-.09L46.894 5.805a.14.14 0 0 0-.126-.09H45.04a.22.22 0 0 0-.152.081l-5.9 9.09-5.901-9.09a.2.2 0 0 0-.152-.081h-1.727c-.056 0-.113.043-.126.09L26.237 21.04c-.**************.07.09H30.9a.15.15 0 0 0 .126-.09l2.377-8.14 4.378 7.115a.2.2 0 0 0 .148.082h1.063zm-12.539-7.59a.1.1 0 0 0-.096-.077H13.272a1.25 1.25 0 0 0-1.254 1.245c0 .69.56 1.246 1.254 1.246h7.775c-.928 2.43-4.056 4.219-7.775 4.219-4.451 0-8.057-2.56-8.057-5.715S8.821 7.71 13.272 7.71c2.43 0 4.608.763 6.083 1.97.005 0 .01.008.013.012a.1.1 0 0 0 .053.018h5.35a.096.096 0 0 0 .095-.095.1.1 0 0 0-.022-.06q0-.009-.009-.01c-2.278-2.408-6.603-4.033-11.567-4.033C5.944 5.503 0 9.05 0 13.424s5.944 7.917 13.272 7.917 13.273-3.542 13.273-7.917q0-.465-.087-.918zm38.259-7.003c-7.328 0-13.272 3.547-13.272 7.917s5.944 7.917 13.272 7.917 13.273-3.543 13.273-7.917-5.94-7.917-13.273-7.917m0 13.632c-4.447 0-8.053-2.56-8.053-5.715s3.606-5.714 8.053-5.714 8.057 2.56 8.057 5.714-3.605 5.715-8.057 5.715"
      />
      <path
        fill="#4B4643"
        d="M82.13 12.032c1.865-.362 2.628-1.835 2.663-5.128H96v6.602c-.034 2.4-.364 3.69-1.267 4.879-1.167 1.538-2.698 2.202-5.163 2.25h-6.83v-2.716h5.732c3.35.018 4.448-1.043 4.53-4.335V9.73H87.34c-.416 3.142-2.048 4.767-5.21 5.163zm21.398 5.775c3.115-.034 4.165-1.19 4.096-4.499V6.426h3.033v6.318c.017 3.141-.3 4.598-1.315 5.84-1.115 1.374-2.981 2.05-5.597 2.05H98.3v-2.827zM98.313 6.49h3.033v8.999h-3.033zm28.896 10.805v2.878h-14.756v-2.878zm-13.424-9.86h12.274v2.796h-12.274zM131.11 8.74a91 91 0 0 1 1.414 5.74h-2.863c-.317-2.05-.669-3.822-1.185-5.74zm10.556-.082v4.797c.152 4.995-1.866 7.296-6.313 7.227h-6.196v-2.513h6.196c2.699-.099 3.679-1.323 3.58-4.434V8.658zm-6.161.082c.599 2.069 1.115 4.167 1.414 5.74h-2.846c-.451-2.465-.681-3.508-1.267-5.74zm8.365 3.292c1.866-.362 2.629-1.835 2.664-5.128h11.207v6.602c-.034 2.4-.364 3.69-1.267 4.879-1.167 1.538-2.698 2.202-5.163 2.25h-6.829v-2.716h5.731c3.35.018 4.448-1.043 4.53-4.335V9.73h-5.662c-.416 3.142-2.048 4.767-5.211 5.163zm19.586.134v3.111h-3.25v-3.111zm1.362-4.93h14.24v3.573c-.018 4.253-1.901 6.136-6.812 6.9.516.973.833 1.62 1.514 3.042h-3.263c-1.297-2.616-2.781-5.176-4.547-7.99h3.298l.815 1.357c.152.263.235.397.651 1.108 4.048-.38 5.38-1.44 5.428-4.335V9.882h-11.324zm18.683 1.504a91 91 0 0 1 1.414 5.74h-2.863c-.317-2.05-.669-3.822-1.185-5.74zm10.56-.082v4.797c.152 4.995-1.865 7.296-6.313 7.227h-6.195v-2.513h6.195c2.699-.099 3.68-1.323 3.58-4.434V8.658zm-6.165.082c.599 2.069 1.115 4.167 1.414 5.74h-2.846c-.451-2.465-.681-3.508-1.267-5.74zm23.468-.168c0-1.414-1.193-2.629-2.611-2.629a2.7 2.7 0 0 0-2.53 1.702h-9.819v2.763h10.075v2.215c.035 4.085-1.332 5.542-5.228 5.542h-4.196v2.728h4.664c5.328-.017 7.745-2.332 7.78-7.395v-2.405a2.655 2.655 0 0 0 1.865-2.53zm-2.677 1.034c-.542 0-1.015-.47-1.015-1.034s.473-1.013 1.015-1.013c.543 0 1.037.448 1.037 1.013s-.451 1.034-1.037 1.034"
      />
      <path
        fill="#F39939"
        d="M224.359 24c-4.642 0-9.636-1.069-9.636-3.413 0-1.733 2.594-2.586 4.768-2.995a.777.777 0 0 1 .911.616.777.777 0 0 1-.62.905c-2.634.495-3.497 1.262-3.497 1.47 0 .435 2.516 1.861 8.074 1.861s8.075-1.43 8.075-1.862c0-.207-.859-.974-3.497-1.47a.77.77 0 0 1-.621-.904.776.776 0 0 1 .911-.616c2.174.409 4.769 1.262 4.769 2.995 0 2.344-4.994 3.413-9.637 3.413"
      />
      <path
        fill="#F39939"
        d="M225.587 19.712a1.843 1.843 0 0 1-2.456 0c-2.117-1.9-3.744-4.288-4.876-5.956q-2.018-2.963-2.018-5.486 0-3.78 2.451-6.03 2.447-2.243 5.676-2.244c2.152 0 4.039.75 5.675 2.245 1.631 1.495 2.451 3.504 2.451 6.029q0 2.52-2.017 5.486c-1.137 1.668-2.76 4.055-4.877 5.956zm-3.015-8.24v.914c0 .663.546 1.206 1.214 1.206h1.15c.668 0 1.215-.543 1.215-1.206v-2.38h2.395c.668 0 1.215-.542 1.215-1.206V7.658c0-.664-.547-1.207-1.215-1.207h-2.395V4.072c0-.663-.547-1.206-1.215-1.206h-1.15c-.668 0-1.214.543-1.214 1.206v2.38h-2.395c-.669 0-1.215.542-1.215 1.206V8.8c0 .664.546 1.207 1.215 1.207h2.395z"
      />
    </g>
    <defs>
      <clipPath id="parts-logo_svg__a">
        <path fill="#fff" d="M0 0h234v24H0z" />
      </clipPath>
    </defs>
  </svg>
);
export default ClinicMapLogo;
